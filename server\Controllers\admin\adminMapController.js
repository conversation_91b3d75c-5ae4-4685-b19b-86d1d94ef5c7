const Drone = require('../../models/Drone');
const Individual = require('../../models/IndividualRegistration');
const Organization = require('../../models/organizationModel');
// dashboard map controller
// this will return the list of all the drones with their location and type
exports.getAllDronesForMap = async (req, res) => {
  try {
    const filter = {};
    if (req.query.status) {
      filter.status = req.query.status;
    }
    // Only select fields needed for the map
    const drones = await Drone.find(filter, 'droneId currentLocation status model batteryLevel');
    res.json({
      drones: drones.map(drone => ({
        _id: drone._id,
        droneId: drone.droneId,
        currentLocation: drone.currentLocation,
        status: drone.status,
        model: drone.model,
        batteryLevel: drone.batteryLevel
      }))
    });
  } catch (err) {
    res.status(500).json({ message: 'Server error' });
  }
};
// this will return the list of all the organizations and individuals with their location and type
exports.getAllOrgindLocations = async (req, res) => {
  console.log('🔵 getAllOrgindLocations endpoint hit');
  try {
    // Fetch organizations with placeName
    const organizations = await Organization.find(
      { placeName: { $exists: true, $ne: null } },
      'orgName registrationNumber placeName poc orgType location' // include location
    );
    // Fetch individuals with placeName
    const individuals = await Individual.find(
      { placeName: { $exists: true, $ne: null } },
      'personalDetails placeName location' // include location
    );
    // Format organization data
    const formattedOrganizations = organizations.map(org => ({
      name: org.orgName,
      code: org.registrationNumber,
      placeName: org.placeName,
      type: 'Organization',
      subtype: org.orgType,
      contactPerson: org.poc?.name || 'N/A',
      latitude: org.location?.latitude || null,
      longitude: org.location?.longitude || null
    }));
    // Format individual data
    const formattedIndividuals = individuals.map(ind => ({
      name: ind.personalDetails.name,
      code: ind.personalDetails.panNumber || 'N/A',
      placeName: ind.placeName,
      type: 'Individual',
      contactPerson: ind.personalDetails.name,
      latitude: ind.location?.latitude || null,
      longitude: ind.location?.longitude || null
    }));
    // Combine both
    const combinedList = [...formattedOrganizations, ...formattedIndividuals];
    res.status(200).json({
      success: true,
      header: "Organizations / Individuals List",
      data: combinedList
    });
  } catch (error) {
    console.error('❌ Error fetching organization/individual locations:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
};
// this will return the drones of a particular org/individual with their location and type
exports.getEntityWithDrones = async (req, res) => {
  try {
    const entityId = req.params.id;
    // Try to find as an organization first
    let entity = await Organization.findById(entityId).select('orgName location');
    let entityType = 'Organization';
    // If not found, try as individual
    if (!entity) {
      entity = await Individual.findById(entityId).select('personalDetails location');
      entityType = 'Individual';
    }
    if (!entity) {
      return res.status(404).json({ message: 'Organization or Individual not found' });
    }
    // Determine the correct drone filter key
    const droneFilter = entityType === 'Organization'
      ? { organizationId: entityId }
      : { individualId: entityId };
    const drones = await Drone.find(droneFilter).select('droneId status currentLocation');
    // Format entity response
    const formattedEntity =
      entityType === 'Organization'
        ? {
            name: entity.orgName,
            type: 'Organization',
            location: entity.location
          }
        : {
            name: entity.personalDetails.name,
            type: 'Individual',
            location: entity.location
          };
    res.status(200).json({
      entity: formattedEntity,
      drones
    });
  } catch (error) {
    console.error('❌ Error fetching entity and drones:', error);
    res.status(500).json({ message: 'Server Error' });
  }
};
