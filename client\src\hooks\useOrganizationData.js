import { useState, useEffect, useCallback, useMemo } from 'react';
import axios from 'axios';
import { getToken } from '../utils/auth';
import { createDebugFetchOrganizations } from '../utils/apiDebug';

const useOrganizationData = ({
  endpoint = 'http://localhost:5000/admin/organization-map',
  refreshInterval = 300000,
  fallbackData = []
}) => {
  const [organizations, setOrganizations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // ✅ Memoized debug instance - created once unless config changes
  const debugFetchOrganizations = useMemo(() => {
    return createDebugFetchOrganizations({
      endpoint,
      refreshInterval,
      fallbackData,
    });
  }, [endpoint, refreshInterval, fallbackData]);

  // ✅ Stable fetch function
  const fetchOrganizations = useCallback(async () => {
    console.log('🔵 fetchOrganizations called');
    try {
      setLoading(true);
      const token = getToken();

      debugFetchOrganizations?.logToken(token);
      debugFetchOrganizations?.logRequest(endpoint);

      const response = await axios.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.data?.success && response.data?.data) {
        setOrganizations(response.data.data);
        setError(null);
      } else {
        throw new Error('Invalid response structure');
      }
    } catch (err) {
      console.error('Error fetching organizations:', err);
      setError('Failed to load organization data');

      if (fallbackData.length > 0) {
        setOrganizations(fallbackData);
      }
    } finally {
      setLoading(false);
    }
  }, [endpoint, fallbackData, debugFetchOrganizations]);

  // ✅ Runs only once unless refreshInterval or fetchOrganizations changes
  useEffect(() => {
    fetchOrganizations();

    let interval;
    if (refreshInterval > 0) {
      interval = setInterval(fetchOrganizations, refreshInterval);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [fetchOrganizations, refreshInterval]);

  const filterOrganizations = (filterFn) => organizations.filter(filterFn);

  const getValidLocations = () => {
    return organizations.filter(org => org.latitude && org.longitude);
  };

  return {
    organizations,
    loading,
    error,
    fetchOrganizations,
    filterOrganizations,
    getValidLocations,
  };
};

export default useOrganizationData;
