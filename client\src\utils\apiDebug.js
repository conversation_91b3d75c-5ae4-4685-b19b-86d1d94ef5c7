/**
 * API Debugging Utility
 * Use this to troubleshoot API requests and responses
 */
import axios from 'axios';
import { getToken } from './auth';

// API base URL from environment or default
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';

/**
 * Debug API call to test connection and authentication
 * @param {string} endpoint - API endpoint to test
 * @returns {Promise} - API response or error information
 */
export const debugApiCall = async (endpoint) => {
  try {
    const token = getToken();
    console.log('Token being used:', token ? `${token.substring(0, 15)}...` : 'No token found');
    
    const config = {
      headers: {
        Authorization: token ? `Bearer ${token}` : '',
      },
    };
    
    console.log('Making test request to:', `${API_BASE_URL}${endpoint}`);
    const response = await axios.get(`${API_BASE_URL}${endpoint}`, config);
    
    console.log('API Response:', {
      status: response.status,
      statusText: response.statusText,
      hasData: !!response.data,
      dataStructure: response.data ? Object.keys(response.data) : 'No data',
      dataFormat: response.data ? (
        response.data.success !== undefined ? 'Success flag found' : 'No success flag'
      ) : 'No data',
    });
    
    return {
      success: true,
      data: response.data,
      status: response.status,
    };
  } catch (error) {
    console.error('API Debug Error:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
    });
    
    return {
      success: false,
      error: error.message,
      status: error.response?.status,
      data: error.response?.data,
    };
  }
};

/**
 * Update the useOrganizationData hook to use the debugged version
 * @param {Object} options - Original hook options
 * @returns {Object} - Debug object with logging methods
 */
export const createDebugFetchOrganizations = (options) => {
  return {
    logToken: (token) => {
      console.log('🔑 Token being used:', token ? `${token.substring(0, 15)}...` : 'No token found');
    },
    logRequest: (endpoint) => {
      console.log('🌐 Making request to:', endpoint);
      console.log('📋 Request options:', options);
    },
    debugFetch: async () => {
      console.log('Debugging organization fetch with options:', options);
      return debugApiCall('/admin/organization-map');
    }
  };
};
