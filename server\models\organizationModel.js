const mongoose = require('mongoose');

const organizationSchema = new mongoose.Schema({
  orgName: String,
  orgType: String,
  registrationNumber: String,
  officialName: String,
  contactNumber: String,
  address: String,
  panNumber: String,
  poc: {
    name: String,
    email: String,
    designation: String,
    kycDocument: String,      // File path or base64
    idProof: String           // File path or base64
  },
  authorizationForm: String  ,
  // New field for storing place name
  placeName: { type: String },
  location: {
    latitude: { type: Number },
    longitude: { type: Number }
  }  // File path or base64
}, { timestamps: true });

module.exports = mongoose.model('Organization', organizationSchema);
